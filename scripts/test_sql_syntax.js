#!/usr/bin/env node

/**
 * Test SQL Syntax for Mock Data Loading
 * 
 * This script validates the SQL syntax without executing it against a database.
 * Useful for catching syntax errors before attempting to load data.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function validateSQL() {
  console.log('🔍 Validating SQL syntax...');
  
  const sqlFilePath = path.join(__dirname, 'load_mock_data.sql');
  
  if (!fs.existsSync(sqlFilePath)) {
    console.error('❌ SQL file not found:', sqlFilePath);
    process.exit(1);
  }
  
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  
  // Basic syntax checks
  const checks = [
    {
      name: 'Balanced parentheses',
      test: () => {
        const openParens = (sqlContent.match(/\(/g) || []).length;
        const closeParens = (sqlContent.match(/\)/g) || []).length;
        return openParens === closeParens;
      }
    },
    {
      name: 'Balanced single quotes',
      test: () => {
        // Count single quotes not preceded by backslash
        const quotes = (sqlContent.match(/(?<!\\)'/g) || []).length;
        return quotes % 2 === 0;
      }
    },
    {
      name: 'No obvious SQL injection patterns',
      test: () => {
        const dangerousPatterns = [
          /;\s*DROP\s+/i,
          /;\s*DELETE\s+FROM\s+(?!compliance_|kyc_|regulatory_|audit_|performance_|notifications|workflow_|system_)/i,
          /;\s*TRUNCATE\s+/i
        ];
        return !dangerousPatterns.some(pattern => pattern.test(sqlContent));
      }
    },
    {
      name: 'Contains expected table names',
      test: () => {
        const expectedTables = [
          'compliance_rules',
          'regulatory_updates', 
          'compliance_documents',
          'kyc_profiles',
          'compliance_violations',
          'compliance_reports',
          'workflow_executions',
          'notifications',
          'performance_metrics',
          'audit_logs'
        ];
        return expectedTables.every(table => sqlContent.includes(table));
      }
    },
    {
      name: 'Contains INSERT statements',
      test: () => {
        return /INSERT\s+INTO/i.test(sqlContent);
      }
    },
    {
      name: 'Contains ON CONFLICT clauses',
      test: () => {
        return /ON\s+CONFLICT/i.test(sqlContent);
      }
    },
    {
      name: 'Contains DO blocks',
      test: () => {
        return /DO\s+\$\$/i.test(sqlContent);
      }
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    try {
      const passed = check.test();
      console.log(`${passed ? '✅' : '❌'} ${check.name}`);
      if (!passed) allPassed = false;
    } catch (error) {
      console.log(`❌ ${check.name}: ${error.message}`);
      allPassed = false;
    }
  });
  
  // Count statements
  const insertCount = (sqlContent.match(/INSERT\s+INTO/gi) || []).length;
  const doBlockCount = (sqlContent.match(/DO\s+\$\$/gi) || []).length;
  
  console.log('\n📊 SQL Statistics:');
  console.log(`   INSERT statements: ${insertCount}`);
  console.log(`   DO blocks: ${doBlockCount}`);
  console.log(`   File size: ${(sqlContent.length / 1024).toFixed(1)} KB`);
  console.log(`   Lines: ${sqlContent.split('\n').length}`);
  
  if (allPassed) {
    console.log('\n✅ SQL syntax validation passed!');
    console.log('💡 You can now run: node scripts/load_mock_data.js');
  } else {
    console.log('\n❌ SQL syntax validation failed!');
    console.log('🔧 Please fix the issues above before running the load script.');
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  validateSQL();
}

export { validateSQL };
