-- Load Mock Data for Compliance Command Center DBOS
-- This script loads all static data from src/index.ts into the database

-- Set timezone for consistent timestamps
SET timezone = 'UTC';

-- =====================================================
-- COMPLIANCE RULES (from COMPLIANCE_RULES array)
-- =====================================================

-- Clear existing rules first (optional - remove if you want to keep existing data)
-- DELETE FROM compliance_rules WHERE rule_id IN ('SEC-001', 'GLBA-001', 'SOX-001');

-- Insert compliance rules from the application
INSERT INTO compliance_rules (rule_id, standard, rule_type, description, pattern, severity, is_active, created_by, version) VALUES
('SEC-001', 'SEC', 'financial_disclosure', 'Financial statements must include quarterly earnings disclosure', 'quarterly.*(earnings|revenue|income)', 'high', true, 'system', 1),
('GLBA-001', 'GLBA', 'privacy', 'Customer financial information must be protected', '(ssn|social.security|account.number|routing.number)', 'critical', true, 'system', 1),
('SOX-001', 'SOX', 'financial_disclosure', 'Internal controls must be documented', 'internal.control.*documentation', 'high', true, 'system', 1)
ON CONFLICT (rule_id) DO UPDATE SET
    description = EXCLUDED.description,
    pattern = EXCLUDED.pattern,
    severity = EXCLUDED.severity,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- REGULATORY UPDATES (from fetchRegulatoryUpdates method)
-- =====================================================

INSERT INTO regulatory_updates (
    update_id, standard, title, description, effective_date, impact, action_required,
    published_date, processed, affected_documents_count, recommended_actions
) VALUES
('SEC-2024-001', 'SEC', 'Updated Cybersecurity Disclosure Requirements', 
 'New requirements for cybersecurity incident reporting within 4 business days', 
 '2024-12-01', 'high', true, '2024-06-01', false, 23,
 ARRAY['URGENT: Review and update policies for Updated Cybersecurity Disclosure Requirements', 'URGENT: Train compliance team on SEC changes']),
('GLBA-2024-002', 'GLBA', 'Enhanced Privacy Notice Requirements',
 'Updated privacy notice requirements for financial institutions',
 '2024-11-15', 'medium', true, '2024-05-15', false, 15,
 ARRAY['PRIORITY: Update procedures for Enhanced Privacy Notice Requirements'])
ON CONFLICT (update_id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    effective_date = EXCLUDED.effective_date,
    impact = EXCLUDED.impact,
    action_required = EXCLUDED.action_required,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- SAMPLE DOCUMENTS (from /api/documents/recent endpoint)
-- =====================================================

INSERT INTO compliance_documents (
    document_id, content, document_type, uploaded_at, status, file_name, file_size, 
    mime_type, uploaded_by, metadata
) VALUES
('DOC-001', 'Q3 Financial Report containing quarterly earnings of $2.5M and revenue growth of 15%. This report includes comprehensive financial disclosure statements and quarterly income analysis as required by SEC regulations.', 
 'financial_report', '2024-06-09 14:30:00+00', 'non_compliant', 'Q3_Financial_Report.pdf', 2516582, 'application/pdf', 'finance_team',
 '{"size_display": "2.4 MB", "violations": 2, "complianceChecks": ["SEC", "SOX", "GLBA"]}'),
('DOC-002', 'Risk Assessment for June 2024 covering operational risks, market risks, and compliance risks. This document provides comprehensive risk analysis and mitigation strategies.',
 'procedure', '2024-06-09 11:15:00+00', 'compliant', 'Risk_Assessment_June.docx', 1887437, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'risk_team',
 '{"size_display": "1.8 MB", "violations": 0, "complianceChecks": ["SEC", "FINRA"]}'),
('DOC-003', 'Customer Agreement version 3 containing terms and conditions, privacy policies, and data handling procedures. This agreement outlines customer rights and company obligations.',
 'contract', '2024-06-09 10:45:00+00', 'processing', 'Customer_Agreement_v3.pdf', 978944, 'application/pdf', 'legal_team',
 '{"size_display": "956 KB", "violations": null, "complianceChecks": ["GLBA", "CCPA"]}'),
('DOC-004', 'Policy Update for May 2024 including updated data protection policies, privacy procedures, and GDPR compliance measures.',
 'policy', '2024-06-08 16:20:00+00', 'compliant', 'Policy_Update_May.pdf', 1258291, 'application/pdf', 'compliance_team',
 '{"size_display": "1.2 MB", "violations": 0, "complianceChecks": ["SOX", "GDPR"]}'),
('DOC-005', 'Q2 Audit Report containing internal control assessments, financial audit results, and compliance verification. Missing some required SOX documentation.',
 'financial_report', '2024-06-08 09:30:00+00', 'non_compliant', 'Audit_Report_Q2.pdf', 3250176, 'application/pdf', 'audit_team',
 '{"size_display": "3.1 MB", "violations": 1, "complianceChecks": ["SEC", "SOX", "FINRA"]}')
ON CONFLICT (document_id) DO UPDATE SET
    content = EXCLUDED.content,
    status = EXCLUDED.status,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- SAMPLE KYC PROFILES (from /api/kyc/queue endpoint)
-- =====================================================

-- Note: Personal information is encrypted in the database
INSERT INTO kyc_profiles (
    customer_id, name_encrypted, date_of_birth_encrypted, ssn_encrypted, address_encrypted,
    risk_score, status, last_updated, identity_verified, identity_confidence,
    sanctions_checked, sanctions_match, created_by, metadata
) VALUES
('KYC-2024-001',
 encode(pgp_sym_encrypt('John Anderson', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1985-03-15', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('***********', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('123 Main St, New York, NY 10001', 'demo_key'), 'base64'),
 25, 'under_review', '2024-06-09 10:30:00+00', true, 0.85, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-09 10:30", "timeRemaining": "2 hours", "completedSteps": 2, "totalSteps": 4, "flags": []}'),
('KYC-2024-002',
 encode(pgp_sym_encrypt('Sarah Michelle Corp', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1990-07-22', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('***********', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('456 Business Ave, Los Angeles, CA 90210', 'demo_key'), 'base64'),
 45, 'under_review', '2024-06-09 09:15:00+00', true, 0.78, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-09 09:15", "timeRemaining": "4 hours", "completedSteps": 3, "totalSteps": 4, "flags": ["Corporate Entity"]}'),
('KYC-2024-003',
 encode(pgp_sym_encrypt('Robert Chen', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1978-11-08', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('555-44-3333', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('789 International Blvd, Miami, FL 33101', 'demo_key'), 'base64'),
 75, 'under_review', '2024-06-08 16:45:00+00', true, 0.72, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-08 16:45", "timeRemaining": "Overdue", "completedSteps": 3, "totalSteps": 4, "flags": ["High Risk Country", "PEP Check"]}'),
('KYC-2024-004',
 encode(pgp_sym_encrypt('Emma Technologies Ltd', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1995-02-14', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('111-22-3333', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('321 Tech Park Dr, Austin, TX 78701', 'demo_key'), 'base64'),
 20, 'approved', '2024-06-08 14:20:00+00', true, 0.92, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-08 14:20", "timeRemaining": "Completed", "completedSteps": 4, "totalSteps": 4, "flags": []}'),
('KYC-2024-005',
 encode(pgp_sym_encrypt('Michael Rodriguez', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1988-09-30', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('777-88-9999', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('654 Residential St, Chicago, IL 60601', 'demo_key'), 'base64'),
 50, 'under_review', '2024-06-08 11:30:00+00', true, 0.80, false, false, 'kyc_system',
 '{"submissionDate": "2024-06-08 11:30", "timeRemaining": "6 hours", "completedSteps": 1, "totalSteps": 4, "flags": ["First Time Customer"]}'),
('KYC-2024-006',
 encode(pgp_sym_encrypt('Global Finance Inc', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1982-12-05', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('444-55-6666', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('987 Corporate Plaza, Boston, MA 02101', 'demo_key'), 'base64'),
 55, 'under_review', '2024-06-08 09:15:00+00', true, 0.75, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-08 09:15", "timeRemaining": "3 hours", "completedSteps": 2, "totalSteps": 4, "flags": ["Corporate Entity", "Multiple Jurisdictions"]}'),
('KYC-2024-007',
 encode(pgp_sym_encrypt('Alice Johnson', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('1992-04-18', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('222-33-4444', 'demo_key'), 'base64'),
 encode(pgp_sym_encrypt('159 Suburban Lane, Seattle, WA 98101', 'demo_key'), 'base64'),
 15, 'approved', '2024-06-07 15:45:00+00', true, 0.95, true, false, 'kyc_system',
 '{"submissionDate": "2024-06-07 15:45", "timeRemaining": "Completed", "completedSteps": 4, "totalSteps": 4, "flags": []}')
ON CONFLICT (customer_id) DO UPDATE SET
    risk_score = EXCLUDED.risk_score,
    status = EXCLUDED.status,
    last_updated = EXCLUDED.last_updated,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- SAMPLE VIOLATIONS (from /api/dashboard/recent-violations endpoint)
-- =====================================================

-- Get document and rule IDs for foreign key references
DO $$
DECLARE
    doc1_id UUID;
    doc2_id UUID;
    doc3_id UUID;
    rule_sec_id UUID;
    rule_glba_id UUID;
    rule_sox_id UUID;
BEGIN
    -- Get document IDs
    SELECT id INTO doc1_id FROM compliance_documents WHERE document_id = 'DOC-001';
    SELECT id INTO doc2_id FROM compliance_documents WHERE document_id = 'DOC-003';
    SELECT id INTO doc3_id FROM compliance_documents WHERE document_id = 'DOC-002';

    -- Get rule IDs
    SELECT id INTO rule_sox_id FROM compliance_rules WHERE rule_id = 'SOX-001';
    SELECT id INTO rule_glba_id FROM compliance_rules WHERE rule_id = 'GLBA-001';
    SELECT id INTO rule_sec_id FROM compliance_rules WHERE rule_id = 'SEC-001';

    -- Insert violations
    INSERT INTO compliance_violations (
        document_id, rule_id, violation_type, description, severity, recommended_action,
        detected_at, status, match_text, match_position, confidence_score, metadata
    ) VALUES
    (doc1_id, rule_sox_id, 'financial_disclosure', 'Missing SOX disclosure statement', 'critical',
     'Immediate remediation required - escalate to legal team', '2024-06-08 00:00:00+00', 'open',
     'quarterly earnings', 45, 0.92, '{"document": "Q3_Financial_Report.pdf", "severity_display": "Critical", "status_display": "Under Review"}'),
    (doc2_id, rule_glba_id, 'privacy', 'GLBA privacy notice incomplete', 'high',
     'Priority remediation - update within 24 hours', '2024-06-07 00:00:00+00', 'open',
     'customer data', 123, 0.87, '{"document": "Customer_Agreement_v2.pdf", "severity_display": "High", "status_display": "Remediation Required"}'),
    (doc3_id, rule_sec_id, 'financial_disclosure', 'SEC reporting format non-compliance', 'medium',
     'Schedule remediation within 1 week', '2024-06-06 00:00:00+00', 'in_progress',
     'financial disclosure', 67, 0.75, '{"document": "Risk_Assessment_May.pdf", "severity_display": "Medium", "status_display": "In Progress"}')
    ON CONFLICT DO NOTHING;
END $$;

-- =====================================================
-- SAMPLE REPORTS (from /api/reports/recent endpoint)
-- =====================================================

INSERT INTO compliance_reports (
    report_id, report_type, generated_at, compliance_rate, executive_summary,
    recommendations, file_path, file_size, file_format, recipients,
    total_documents, compliant_documents, violations_count, critical_violations,
    high_violations, medium_violations, low_violations, period_start, period_end, metadata
) VALUES
('RPT-2024-045', 'quarterly', '2024-06-09 09:30:00+00', 98.20,
 'Q2 2024 Compliance Summary showing strong overall compliance with minor violations requiring attention.',
 ARRAY['Review SOX disclosure requirements', 'Update GLBA privacy notices', 'Enhance document review processes'],
 '/reports/RPT-2024-045.pdf', 2516582, 'PDF', ARRAY['Board', 'Executive Team'],
 1247, 1224, 23, 2, 5, 8, 8, '2024-04-01', '2024-06-30',
 '{"name": "Q2 2024 Compliance Summary", "type": "Compliance Summary", "size_display": "2.4 MB", "pages": 15}'),
('RPT-2024-044', 'monthly', '2024-06-08 16:45:00+00', 94.50,
 'June KYC Status Report showing processing efficiency and risk assessment outcomes.',
 ARRAY['Improve high-risk customer review process', 'Enhance sanctions screening automation'],
 '/reports/RPT-2024-044.pdf', 1887437, 'PDF', ARRAY['Operations', 'Compliance'],
 156, 147, 9, 0, 2, 4, 3, '2024-06-01', '2024-06-30',
 '{"name": "June KYC Status Report", "type": "KYC Status", "size_display": "1.8 MB", "pages": 8}'),
('RPT-2024-043', 'incident', '2024-06-07 14:20:00+00', 85.30,
 'May Violations Analysis identifying patterns and remediation strategies.',
 ARRAY['Implement automated violation detection', 'Enhance staff training programs'],
 '/reports/RPT-2024-043.pdf', NULL, 'PDF', ARRAY['Compliance Team'],
 89, 76, 13, 1, 3, 5, 4, '2024-05-01', '2024-05-31',
 '{"name": "May Violations Analysis", "type": "Violations Analysis", "size_display": "Pending", "pages": "Pending", "status": "Generating"}'),
('RPT-2024-042', 'quarterly', '2024-06-06 10:15:00+00', 96.80,
 'Q2 Regulatory Impact Assessment analyzing new regulations and their organizational impact.',
 ARRAY['Prepare for SEC cybersecurity requirements', 'Update GLBA privacy procedures'],
 '/reports/RPT-2024-042.pdf', 3355443, 'PDF', ARRAY['Executives', 'Legal'],
 234, 226, 8, 0, 1, 3, 4, '2024-04-01', '2024-06-30',
 '{"name": "Q2 Regulatory Impact Assessment", "type": "Regulatory Impact", "size_display": "3.2 MB", "pages": 22}'),
('RPT-2024-041', 'monthly', '2024-06-05 13:45:00+00', 99.10,
 'May Audit Trail Report providing comprehensive activity logging and compliance verification.',
 ARRAY['Maintain current audit standards', 'Continue automated logging processes'],
 '/reports/RPT-2024-041.pdf', 5349376, 'PDF', ARRAY['Auditors', 'Regulators'],
 445, 441, 4, 0, 0, 2, 2, '2024-05-01', '2024-05-31',
 '{"name": "May Audit Trail Report", "type": "Audit Trail", "size_display": "5.1 MB", "pages": 35}')
ON CONFLICT (report_id) DO UPDATE SET
    compliance_rate = EXCLUDED.compliance_rate,
    generated_at = EXCLUDED.generated_at,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- SAMPLE WORKFLOW EXECUTIONS (from /api/workflows/active endpoint)
-- =====================================================

-- Get document and report IDs for foreign key references
DO $$
DECLARE
    doc1_id UUID;
    doc2_id UUID;
    doc3_id UUID;
    kyc1_id UUID;
    kyc2_id UUID;
    rpt1_id UUID;
BEGIN
    -- Get document IDs
    SELECT id INTO doc1_id FROM compliance_documents WHERE document_id = 'DOC-001';
    SELECT id INTO doc2_id FROM compliance_documents WHERE document_id = 'DOC-002';
    SELECT id INTO doc3_id FROM compliance_documents WHERE document_id = 'DOC-003';

    -- Get KYC profile IDs
    SELECT id INTO kyc1_id FROM kyc_profiles WHERE customer_id = 'KYC-2024-001';
    SELECT id INTO kyc2_id FROM kyc_profiles WHERE customer_id = 'KYC-2024-002';

    -- Get report ID
    SELECT id INTO rpt1_id FROM compliance_reports WHERE report_id = 'RPT-2024-045';

    -- Insert workflow executions
    INSERT INTO workflow_executions (
        workflow_id, workflow_name, workflow_type, status, started_at, completed_at,
        duration_ms, input_data, output_data, document_id, kyc_profile_id, report_id,
        executor_id, queue_name, retry_count, metadata
    ) VALUES
    ('WF-COMP-2024-156', 'processComplianceDocument', 'compliance_check', 'running',
     '2024-06-09 14:30:00+00', NULL, NULL,
     '{"document": "Q3_Financial_Report.pdf"}', NULL, doc1_id, NULL, NULL,
     'DBOS-Worker-01', 'compliance_checks', 0,
     '{"type": "Compliance Check", "progress": 75, "currentStep": "AI Violation Detection", "totalSteps": 4, "estimatedCompletion": "2024-06-09 14:35:00"}'),
    ('WF-KYC-2024-089', 'processKYCCustomer', 'kyc_process', 'running',
     '2024-06-09 14:15:00+00', NULL, NULL,
     '{"customer": "John Anderson"}', NULL, NULL, kyc1_id, NULL,
     'DBOS-Worker-02', 'kyc_processing', 0,
     '{"type": "KYC Process", "progress": 50, "currentStep": "Sanctions Screening", "totalSteps": 4, "estimatedCompletion": "2024-06-09 14:45:00"}'),
    ('WF-REP-2024-034', 'generateComplianceReport', 'report_generation', 'paused',
     '2024-06-09 13:45:00+00', NULL, NULL,
     '{"reportType": "monthly"}', NULL, NULL, NULL, rpt1_id,
     'DBOS-Worker-03', 'report_generation', 0,
     '{"type": "Report Generation", "progress": 25, "currentStep": "Data Collection", "totalSteps": 5, "estimatedCompletion": "Paused"}'),
    ('WF-MON-2024-012', 'weeklyRegulatoryMonitoring', 'regulatory_monitoring', 'completed',
     '2024-06-09 14:00:00+00', '2024-06-09 14:05:00+00', 300000,
     '{"scheduledTime": "2024-06-09 14:00:00"}', '{"updates": 2, "recommendations": 3}', NULL, NULL, NULL,
     'DBOS-Worker-04', 'regulatory_monitoring', 0,
     '{"type": "Regulatory Monitoring", "progress": 100, "currentStep": "Completed", "totalSteps": 3, "estimatedCompletion": "2024-06-09 14:05:00"}'),
    ('WF-COMP-2024-157', 'processComplianceDocument', 'compliance_check', 'running',
     '2024-06-09 14:20:00+00', NULL, NULL,
     '{"document": "Risk_Assessment_June.docx"}', NULL, doc2_id, NULL, NULL,
     'DBOS-Worker-05', 'compliance_checks', 0,
     '{"type": "Compliance Check", "progress": 90, "currentStep": "Final Validation", "totalSteps": 4, "estimatedCompletion": "2024-06-09 14:25:00"}')
    ON CONFLICT (workflow_id) DO UPDATE SET
        status = EXCLUDED.status,
        completed_at = EXCLUDED.completed_at,
        duration_ms = EXCLUDED.duration_ms,
        output_data = EXCLUDED.output_data,
        updated_at = CURRENT_TIMESTAMP;
END $$;

-- =====================================================
-- SAMPLE NOTIFICATIONS (based on violations and system events)
-- =====================================================

-- Get violation IDs for foreign key references
DO $$
DECLARE
    viol1_id UUID;
    viol2_id UUID;
    viol3_id UUID;
    doc1_id UUID;
    kyc1_id UUID;
    reg1_id UUID;
BEGIN
    -- Get violation IDs (first 3 violations)
    SELECT id INTO viol1_id FROM compliance_violations ORDER BY detected_at DESC LIMIT 1 OFFSET 0;
    SELECT id INTO viol2_id FROM compliance_violations ORDER BY detected_at DESC LIMIT 1 OFFSET 1;
    SELECT id INTO viol3_id FROM compliance_violations ORDER BY detected_at DESC LIMIT 1 OFFSET 2;

    -- Get other IDs
    SELECT id INTO doc1_id FROM compliance_documents WHERE document_id = 'DOC-001';
    SELECT id INTO kyc1_id FROM kyc_profiles WHERE customer_id = 'KYC-2024-003';
    SELECT id INTO reg1_id FROM regulatory_updates WHERE update_id = 'SEC-2024-001';

    -- Insert notifications
    INSERT INTO notifications (
        notification_type, title, message, severity, target_users, target_roles,
        document_id, violation_id, kyc_profile_id, regulatory_update_id, workflow_id,
        sent_at, delivery_method, delivery_status, metadata
    ) VALUES
    ('violation_alert', 'Critical Compliance Violation Detected',
     'A critical SOX disclosure violation has been detected in Q3_Financial_Report.pdf requiring immediate attention.',
     'critical', ARRAY['compliance_manager', 'legal_counsel'], ARRAY['compliance', 'legal'],
     doc1_id, viol1_id, NULL, NULL, 'WF-COMP-2024-156',
     '2024-06-08 00:05:00+00', 'email', 'delivered',
     '{"document": "Q3_Financial_Report.pdf", "violation": "Missing SOX disclosure statement"}'),
    ('kyc_review', 'High-Risk KYC Profile Requires Manual Review',
     'Customer Robert Chen has been flagged for manual review due to high risk score and PEP check requirements.',
     'high', ARRAY['kyc_analyst', 'compliance_manager'], ARRAY['kyc', 'compliance'],
     NULL, NULL, kyc1_id, NULL, 'WF-KYC-2024-089',
     '2024-06-08 16:50:00+00', 'email', 'delivered',
     '{"customer": "Robert Chen", "riskScore": "High", "flags": ["High Risk Country", "PEP Check"]}'),
    ('regulatory_update', 'New SEC Cybersecurity Requirements',
     'SEC has published new cybersecurity disclosure requirements effective December 1, 2024. Action required to ensure compliance.',
     'high', ARRAY['compliance_manager', 'ciso'], ARRAY['compliance', 'security'],
     NULL, NULL, NULL, reg1_id, 'WF-MON-2024-012',
     '2024-06-01 09:00:00+00', 'email', 'delivered',
     '{"standard": "SEC", "effectiveDate": "2024-12-01", "impact": "high", "actionRequired": true}'),
    ('system_alert', 'Workflow Execution Paused',
     'Report generation workflow WF-REP-2024-034 has been paused and requires manual intervention.',
     'medium', ARRAY['system_admin'], ARRAY['operations'],
     NULL, NULL, NULL, NULL, 'WF-REP-2024-034',
     '2024-06-09 13:50:00+00', 'in_app', 'delivered',
     '{"workflow": "WF-REP-2024-034", "type": "Report Generation", "status": "paused"}}')
    ON CONFLICT DO NOTHING;
END $$;

-- =====================================================
-- SAMPLE PERFORMANCE METRICS (based on dashboard data)
-- =====================================================

INSERT INTO performance_metrics (
    metric_name, metric_category, metric_value, metric_unit, recorded_at,
    period_start, period_end, aggregation_level, workflow_type, document_type,
    compliance_standard, dimensions, metadata
) VALUES
-- Compliance metrics
('compliance_rate', 'compliance', 98.20, 'percentage', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"source": "dashboard"}', '{"description": "Overall compliance rate"}'),
('active_violations', 'compliance', 3, 'count', '2024-06-09 14:00:00+00',
 '2024-06-09 00:00:00+00', '2024-06-09 23:59:59+00', 'real_time', NULL, NULL, NULL,
 '{"severity": "all"}', '{"description": "Currently active violations"}'),
('documents_scanned', 'compliance', 1247, 'count', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"period": "month_to_date"}', '{"description": "Total documents scanned this month"}'),
('violation_rate', 'compliance', 1.8, 'percentage', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"calculation": "violations/total_documents"}', '{"description": "Violation detection rate"}'),

-- KYC metrics
('pending_kyc', 'kyc', 24, 'count', '2024-06-09 14:00:00+00',
 '2024-06-09 00:00:00+00', '2024-06-09 23:59:59+00', 'real_time', NULL, NULL, NULL,
 '{"status": "pending"}', '{"description": "KYC profiles pending review"}'),
('kyc_processing_time', 'kyc', 2.3, 'days', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"calculation": "average"}', '{"description": "Average KYC processing time"}'),
('kyc_automation_rate', 'kyc', 94, 'percentage', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"automated_vs_manual": "automated"}', '{"description": "Percentage of automated KYC processing"}'),

-- Workflow metrics
('workflow_success_rate', 'workflow', 99.7, 'percentage', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"status": "completed_successfully"}', '{"description": "Workflow execution success rate"}'),
('avg_execution_time', 'workflow', 3.2, 'minutes', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"calculation": "average"}', '{"description": "Average workflow execution time"}'),
('concurrent_workflows', 'workflow', 15, 'count', '2024-06-09 14:00:00+00',
 '2024-06-09 14:00:00+00', '2024-06-09 14:00:00+00', 'real_time', NULL, NULL, NULL,
 '{"status": "running"}', '{"description": "Currently running workflows"}'),

-- System metrics
('document_processing_time', 'system', 2.3, 'seconds', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', 'compliance_check', NULL, NULL,
 '{"calculation": "average"}', '{"description": "Average document processing time"}'),
('system_uptime', 'system', 99.9, 'percentage', '2024-06-09 14:00:00+00',
 '2024-06-01 00:00:00+00', '2024-06-09 23:59:59+00', 'daily', NULL, NULL, NULL,
 '{"availability": "uptime"}', '{"description": "System availability percentage"}'),
('api_response_time', 'system', 85, 'milliseconds', '2024-06-09 14:00:00+00',
 '2024-06-09 13:00:00+00', '2024-06-09 14:00:00+00', 'hourly', NULL, NULL, NULL,
 '{"calculation": "average"}', '{"description": "Average API response time"}')
ON CONFLICT DO NOTHING;

-- =====================================================
-- SAMPLE AUDIT LOGS (based on system activities)
-- =====================================================

INSERT INTO audit_logs (
    event_type, event_category, event_description, user_id, user_name, user_role,
    ip_address, target_type, target_id, target_name, old_values, new_values,
    workflow_id, risk_level, compliance_impact, requires_review, metadata, occurred_at
) VALUES
('document_upload', 'compliance', 'Document uploaded for compliance checking', 'finance_team', 'Finance Team', 'finance',
 '*************', 'document', 'DOC-001', 'Q3_Financial_Report.pdf', NULL,
 '{"status": "pending", "document_type": "financial_report"}', NULL, 'low', true, false,
 '{"file_size": "2.4 MB", "mime_type": "application/pdf"}', '2024-06-09 14:30:00+00'),

('violation_detected', 'compliance', 'Critical compliance violation detected', 'system', 'Compliance System', 'system',
 NULL, 'violation', 'VIOL-001', 'Missing SOX disclosure statement', NULL,
 '{"severity": "critical", "status": "open"}', 'WF-COMP-2024-156', 'critical', true, true,
 '{"document": "Q3_Financial_Report.pdf", "rule": "SOX-001"}', '2024-06-08 00:00:00+00'),

('kyc_status_change', 'kyc', 'KYC profile status updated to under review', 'kyc_analyst', 'KYC Analyst', 'kyc',
 '192.168.1.105', 'kyc_profile', 'KYC-2024-003', 'Robert Chen', '{"status": "pending"}',
 '{"status": "under_review", "risk_score": 75}', 'WF-KYC-2024-089', 'high', true, true,
 '{"reason": "High risk score and PEP check required"}', '2024-06-08 16:45:00+00'),

('report_generated', 'reporting', 'Compliance report generated successfully', 'system', 'Report Generator', 'system',
 NULL, 'report', 'RPT-2024-045', 'Q2 2024 Compliance Summary', NULL,
 '{"status": "completed", "compliance_rate": 98.20}', 'WF-REP-2024-045', 'low', false, false,
 '{"pages": 15, "recipients": ["Board", "Executive Team"]}', '2024-06-09 09:30:00+00'),

('regulatory_update', 'regulatory', 'New regulatory update processed', 'system', 'Regulatory Monitor', 'system',
 NULL, 'regulatory_update', 'SEC-2024-001', 'Updated Cybersecurity Disclosure Requirements', NULL,
 '{"processed": true, "impact": "high"}', 'WF-MON-2024-012', 'high', true, true,
 '{"effective_date": "2024-12-01", "action_required": true}', '2024-06-01 09:00:00+00'),

('workflow_started', 'system', 'Compliance workflow execution started', 'system', 'DBOS Workflow Engine', 'system',
 NULL, 'workflow', 'WF-COMP-2024-156', 'processComplianceDocument', NULL,
 '{"status": "running", "queue": "compliance_checks"}', 'WF-COMP-2024-156', 'low', false, false,
 '{"document": "Q3_Financial_Report.pdf", "executor": "DBOS-Worker-01"}', '2024-06-09 14:30:00+00'),

('user_login', 'system', 'User logged into compliance dashboard', 'compliance_manager', 'Compliance Manager', 'compliance',
 '*************', 'user_session', 'SESSION-001', 'Dashboard Access', NULL,
 '{"session_started": true, "dashboard_access": true}', NULL, 'low', false, false,
 '{"user_agent": "Mozilla/5.0", "login_method": "sso"}', '2024-06-09 08:30:00+00')
ON CONFLICT DO NOTHING;

-- =====================================================
-- SUMMARY AND VERIFICATION
-- =====================================================

-- Display summary of loaded data
DO $$
BEGIN
    RAISE NOTICE '=== MOCK DATA LOADING SUMMARY ===';
    RAISE NOTICE 'Compliance Rules: % records', (SELECT COUNT(*) FROM compliance_rules);
    RAISE NOTICE 'Regulatory Updates: % records', (SELECT COUNT(*) FROM regulatory_updates);
    RAISE NOTICE 'Documents: % records', (SELECT COUNT(*) FROM compliance_documents);
    RAISE NOTICE 'KYC Profiles: % records', (SELECT COUNT(*) FROM kyc_profiles);
    RAISE NOTICE 'Violations: % records', (SELECT COUNT(*) FROM compliance_violations);
    RAISE NOTICE 'Reports: % records', (SELECT COUNT(*) FROM compliance_reports);
    RAISE NOTICE 'Workflow Executions: % records', (SELECT COUNT(*) FROM workflow_executions);
    RAISE NOTICE 'Notifications: % records', (SELECT COUNT(*) FROM notifications);
    RAISE NOTICE 'Performance Metrics: % records', (SELECT COUNT(*) FROM performance_metrics);
    RAISE NOTICE 'Audit Logs: % records', (SELECT COUNT(*) FROM audit_logs);
    RAISE NOTICE '=== LOADING COMPLETE ===';
END $$;
