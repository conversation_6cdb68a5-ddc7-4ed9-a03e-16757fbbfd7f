#!/usr/bin/env node

/**
 * Load Mock Data Script for Compliance Command Center DBOS
 * 
 * This script loads all static data from src/index.ts into the database
 * according to the database_schema.sql structure.
 * 
 * Usage:
 *   node scripts/load_mock_data.js [--force] [--verbose]
 * 
 * Options:
 *   --force    Clear existing data before loading (destructive)
 *   --verbose  Show detailed output
 */

import fs from 'fs';
import path from 'path';
import { Client } from 'pg';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'dbos_kyc_demo',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD || 'password',
  // Use connection string if provided
  connectionString: process.env.DBOS_DATABASE_URL || process.env.DATABASE_URL
};

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help') || args.includes('-h')
};

// Help text
if (options.help) {
  console.log(`
Load Mock Data Script for Compliance Command Center DBOS

Usage: node scripts/load_mock_data.js [options]

Options:
  --force     Clear existing data before loading (destructive)
  --verbose   Show detailed output
  --help, -h  Show this help message

Environment Variables:
  DBOS_DATABASE_URL  Full database connection string
  PGHOST            Database host (default: localhost)
  PGPORT            Database port (default: 5432)
  PGDATABASE        Database name (default: dbos_kyc_demo)
  PGUSER            Database user (default: postgres)
  PGPASSWORD        Database password (default: password)

Examples:
  node scripts/load_mock_data.js
  node scripts/load_mock_data.js --verbose
  node scripts/load_mock_data.js --force --verbose
  DBOS_DATABASE_URL="********************************/db" node scripts/load_mock_data.js
`);
  process.exit(0);
}

// Logging utility
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : level === 'success' ? '✅' : 'ℹ️';
  
  if (level === 'verbose' && !options.verbose) {
    return;
  }
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

// Main execution function
async function loadMockData() {
  const client = new Client(config);
  
  try {
    log('🚀 Starting mock data loading process...');
    
    // Connect to database
    log('📡 Connecting to database...', 'verbose');
    await client.connect();
    log('✅ Connected to database successfully');
    
    // Check if database schema exists
    log('🔍 Checking database schema...', 'verbose');
    const schemaCheck = await client.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('compliance_documents', 'compliance_rules', 'kyc_profiles')
    `);
    
    if (parseInt(schemaCheck.rows[0].table_count) < 3) {
      log('❌ Database schema not found. Please run the schema migration first:', 'error');
      log('   psql -d your_database -f database_schema.sql', 'error');
      process.exit(1);
    }
    
    log('✅ Database schema verified');
    
    // Set encryption key for demo purposes
    log('🔐 Setting up encryption key...', 'verbose');
    await client.query("SET app.encryption_key = 'demo_key'");
    
    // Clear existing data if force option is used
    if (options.force) {
      log('🧹 Clearing existing data (--force option enabled)...', 'warn');
      
      const clearQueries = [
        'DELETE FROM audit_logs',
        'DELETE FROM performance_metrics',
        'DELETE FROM notifications',
        'DELETE FROM workflow_events',
        'DELETE FROM workflow_executions',
        'DELETE FROM compliance_report_violations',
        'DELETE FROM compliance_reports',
        'DELETE FROM compliance_violations',
        'DELETE FROM kyc_profiles',
        'DELETE FROM compliance_documents',
        'DELETE FROM regulatory_updates',
        'DELETE FROM compliance_rules WHERE rule_id IN (\'SEC-001\', \'GLBA-001\', \'SOX-001\')'
      ];
      
      for (const query of clearQueries) {
        try {
          const result = await client.query(query);
          log(`   Cleared ${result.rowCount} records from ${query.split(' ')[2]}`, 'verbose');
        } catch (error) {
          log(`   Warning: Could not clear ${query.split(' ')[2]}: ${error.message}`, 'warn');
        }
      }
      
      log('✅ Existing data cleared');
    }
    
    // Read and execute the SQL file
    log('📄 Reading mock data SQL file...', 'verbose');
    const sqlFilePath = path.join(__dirname, 'load_mock_data.sql');
    
    if (!fs.existsSync(sqlFilePath)) {
      log('❌ SQL file not found: ' + sqlFilePath, 'error');
      process.exit(1);
    }
    
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    log('✅ SQL file loaded successfully');
    
    // Execute the SQL
    log('⚡ Executing mock data insertion...', 'verbose');
    await client.query(sqlContent);
    log('✅ Mock data loaded successfully');
    
    // Verify data was loaded
    log('🔍 Verifying loaded data...', 'verbose');
    const verificationQueries = [
      { name: 'Compliance Rules', query: 'SELECT COUNT(*) FROM compliance_rules' },
      { name: 'Regulatory Updates', query: 'SELECT COUNT(*) FROM regulatory_updates' },
      { name: 'Documents', query: 'SELECT COUNT(*) FROM compliance_documents' },
      { name: 'KYC Profiles', query: 'SELECT COUNT(*) FROM kyc_profiles' },
      { name: 'Violations', query: 'SELECT COUNT(*) FROM compliance_violations' },
      { name: 'Reports', query: 'SELECT COUNT(*) FROM compliance_reports' },
      { name: 'Workflow Executions', query: 'SELECT COUNT(*) FROM workflow_executions' },
      { name: 'Notifications', query: 'SELECT COUNT(*) FROM notifications' },
      { name: 'Performance Metrics', query: 'SELECT COUNT(*) FROM performance_metrics' },
      { name: 'Audit Logs', query: 'SELECT COUNT(*) FROM audit_logs' }
    ];
    
    log('📊 Data Loading Summary:', 'success');
    let totalRecords = 0;
    
    for (const { name, query } of verificationQueries) {
      try {
        const result = await client.query(query);
        const count = parseInt(result.rows[0].count);
        totalRecords += count;
        log(`   ${name}: ${count} records`, 'success');
      } catch (error) {
        log(`   ${name}: Error - ${error.message}`, 'error');
      }
    }
    
    log(`🎉 Successfully loaded ${totalRecords} total records!`, 'success');
    
    // Additional verification - check for sample data
    log('🔍 Verifying sample data integrity...', 'verbose');
    
    const sampleChecks = [
      { 
        name: 'SEC-001 Rule', 
        query: "SELECT rule_id FROM compliance_rules WHERE rule_id = 'SEC-001'",
        expected: 1 
      },
      { 
        name: 'Q3 Financial Report', 
        query: "SELECT document_id FROM compliance_documents WHERE document_id = 'DOC-001'",
        expected: 1 
      },
      { 
        name: 'John Anderson KYC', 
        query: "SELECT customer_id FROM kyc_profiles WHERE customer_id = 'KYC-2024-001'",
        expected: 1 
      }
    ];
    
    for (const { name, query, expected } of sampleChecks) {
      try {
        const result = await client.query(query);
        if (result.rows.length === expected) {
          log(`   ✅ ${name}: Found`, 'verbose');
        } else {
          log(`   ⚠️ ${name}: Expected ${expected}, found ${result.rows.length}`, 'warn');
        }
      } catch (error) {
        log(`   ❌ ${name}: Error - ${error.message}`, 'error');
      }
    }
    
    log('🎯 Mock data loading completed successfully!', 'success');
    log('💡 You can now start the application and see the loaded data in the dashboard.', 'success');
    
  } catch (error) {
    log(`❌ Error loading mock data: ${error.message}`, 'error');
    if (options.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  } finally {
    await client.end();
    log('📡 Database connection closed', 'verbose');
  }
}

// Execute the script
if (import.meta.url === `file://${process.argv[1]}`) {
  loadMockData().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

export { loadMockData };
